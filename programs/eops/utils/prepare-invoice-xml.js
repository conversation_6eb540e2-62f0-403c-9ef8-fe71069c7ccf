import _ from 'lodash';
import xml2js from 'xml2js';
import {v4 as uuid} from 'uuid';
import writtenNumber from 'framework/written-number';
import {toUpper, trim} from 'framework/helpers';

export default async function prepareInvoiceXML(app, {eInvoiceId, issueDate, invoiceDocumentNo, templateId}) {
    // General.
    const xmlBuilder = new xml2js.Builder({
        explicitRoot: false,
        xmldec: {version: '1.0', encoding: 'UTF-8'},
        cdata: true
    });
    const invoiceUUID = uuid();
    const round = app.roundNumber;
    const useOutputPrecision = app.setting('eops.useOutputPrecisionsOnEInvoices');
    const currencyPrecision = useOutputPrecision
        ? app.setting('system.outputCurrencyPrecision') || app.setting('system.currencyPrecision')
        : app.setting('system.currencyPrecision');
    const eInvoice = await app.collection('eops.e-invoices').get(eInvoiceId);
    const company = {
        ...(await app.collection('kernel.company').findOne({})),
        isCompany: true
    };

    // Evaluate multi company.
    if (!!app.setting('system.multiCompany')) {
        const branch = await app.collection('kernel.branches').findOne({
            _id: eInvoice.invoiceBranchId,
            $select: ['companyId'],
            $disableActiveCheck: true,
            $disableSoftDelete: true
        });
        const subCompanies = company.subCompanies || [];
        const subCompany = subCompanies.find(subCompany => subCompany.id === branch.companyId);
        if (!subCompany) {
            throw new Error('Sub-company is not found!');
        }

        company.name = subCompany.name;
        company.tagline = subCompany.tagline;
        company.email = subCompany.email;
        company.website = subCompany.website;
        company.legalName = subCompany.legalName;
        company.tin = subCompany.tin;
        company.taxDepartment = subCompany.taxDepartment;
        company.firstName = subCompany.firstName;
        company.lastName = subCompany.lastName;
        company.identity = subCompany.identity;
        company.mersisNo = subCompany.mersisNo;
        company.ticaretSicilNo = subCompany.ticaretSicilNo;
        company.phone = subCompany.phone;
        company.phoneCountryCode = subCompany.phoneCountryCode;
        company.phoneNumbers = subCompany.phoneNumbers;
        company.address = subCompany.address;
    }

    const invoice = await app.collection(eInvoice.invoiceCollection).get(eInvoice.invoiceId);
    const partner = await app.collection('kernel.partners').get(invoice.partnerId);
    const currency = await app.collection('kernel.currencies').findOne({
        _id: invoice.currencyId,
        $select: ['name'],
        $disableActiveCheck: true,
        $disableSoftDelete: true
    });
    const currencyName = currency.name === 'TL' ? 'TRY' : currency.name;
    const companyCurrencyName = company.currency.name === 'TL' ? 'TRY' : company.currency.name;
    const scopeRate = invoice.scopeRate || 1;
    const mersisNo = !!company.mersisNo ? company.mersisNo : app.setting('eops.mersisNo');
    const ticaretSicilNo = !!company.ticaretSicilNo ? company.ticaretSicilNo : app.setting('eops.ticaretSicilNo');

    function groupedTaxes(taxes) {
        const groupedTaxes = _.groupBy(taxes, tax => (!!tax.label ? tax.label : tax.name));
        const items = [];

        for (const label of Object.keys(groupedTaxes)) {
            const appliedTaxes = groupedTaxes[label];
            let total = 0;
            let unTotal = 0;

            for (const tax of appliedTaxes) {
                total += tax.appliedAmount ?? 0;
                unTotal += tax.unAppliedAmount ?? 0;
            }

            items.push({
                ...appliedTaxes[0],
                appliedAmount: round(total, currencyPrecision),
                unAppliedAmount: round(unTotal, currencyPrecision)
            });
        }

        return items;
    }

    // Evaluate multi branch.
    if (!!app.setting('system.multiBranch')) {
        const branch = await app.collection('kernel.branches').findOne({
            _id: invoice.branchId,
            $select: ['name', 'address'],
            $disableActiveCheck: true,
            $disableSoftDelete: true
        });

        if (!!branch) {
            company.name = `${company.name} - ${branch.name}`;
            company.legalName = `${company.legalName} - ${branch.name}`;
            company.address = branch.address;
        }
    }

    // Update partner.
    partner.isCompany = typeof eInvoice.partnerIsCompany === 'boolean' ? eInvoice.partnerIsCompany : partner.isCompany;
    partner.address = eInvoice.invoiceAddress;
    if (partner.isCompany) {
        partner.name = eInvoice.partnerName;
        partner.legalName = eInvoice.partnerName;
        partner.tin = eInvoice.partnerTinIdentity;
        partner.taxDepartment = eInvoice.partnerTaxDepartment;
    } else {
        const parts = eInvoice.partnerName.split(' ');

        if (!!eInvoice.partnerIsCompany) {
            partner.isCompany = true;
        }

        partner.name = eInvoice.partnerName;
        partner.legalName = eInvoice.partnerName;
        partner.lastName = parts.pop();
        partner.firstName = parts.join(' ');
        partner.identity = eInvoice.partnerTinIdentity;
        partner.taxDepartment = eInvoice.partnerTaxDepartment;
    }
    partner.passportNo = eInvoice.partnerPassportNo;

    // Determine parties.
    const supplier = company;
    const customer = partner;

    // Check document no.
    if (invoiceDocumentNo.length !== 16) {
        throw new Error('Invalid document no!');
    }

    // Scenario code.
    const scenarioCodes = {
        'basic-invoice': 'TEMELFATURA',
        'commercial-invoice': 'TICARIFATURA',
        'e-archive-invoice': 'EARSIVFATURA',
        'export-invoice': 'IHRACAT',
        'passenger-invoice': 'YOLCUBERABERFATURA',
        'government-invoice': 'KAMU' // ?
    };
    const scenarioCode = scenarioCodes[eInvoice.invoiceScenario];

    // E-Invoice type code.
    const eInvoiceType = await app.collection('eops.e-invoice-types').findOne({
        _id: eInvoice.eInvoiceTypeId
    });
    const eInvoiceTypeCode = eInvoiceType.code;
    let exemptionReasonCode = null;
    let exemptionReason = null;
    if (eInvoiceTypeCode === 'ISTISNA' && invoice.eInvoiceTypeConditionCode) {
        const condition = eInvoiceType.conditions.find(c => c.code === invoice.eInvoiceTypeConditionCode);

        if (!!condition) {
            exemptionReasonCode = condition.code;
            exemptionReason = condition.name;
        }
    }

    // Tax codes.
    const taxes = {};
    const taxCodes = {};
    for (const tc of app.setting('eops.taxCodes') || []) {
        taxCodes[tc.taxId] = tc.code;
        taxes[tc.taxId] = tc;

        if (_.isString(taxCodes[tc.taxId])) {
            taxCodes[tc.taxId] = taxCodes[tc.taxId].trim();
        }
    }

    // Unit codes.
    const unitCodes = {};
    for (const uc of app.setting('eops.unitCodes') || []) {
        if (!!unitCodes[uc.unitId]) {
            continue;
        }

        unitCodes[uc.unitId] = uc.code;

        if (_.isString(unitCodes[uc.unitId])) {
            unitCodes[uc.unitId] = unitCodes[uc.unitId].trim();
        }
    }

    // Country codes.
    const countries = await app.collection('kernel.countries').find({});
    const countryNames = {};
    for (const country of countries) {
        countryNames[country._id] = toUpper(country.name);

        if (_.isString(countryNames[country._id])) {
            countryNames[country._id] = countryNames[country._id].trim();
        }
    }

    // Product map.
    const products = await app.collection('inventory.products').find({
        _id: {$in: _.uniq(invoice.items.map(item => item.productId))},
        $select: ['code', 'name', 'definition', 'barcode', 'brandId', 'hsCode', 'countryOfOriginId'],
        $populate: [{field: 'brand'}],
        $disableActiveCheck: true,
        $disableSoftDelete: true
    });
    const productsMap = {};
    for (const product of products) {
        productsMap[product._id] = product;
    }

    // Get bank accounts.
    const bankAccounts = await app.collection('accounting.bank-accounts').find({
        iban: {$exists: true},
        useInformationInRelatedDocuments: true,
        $select: ['name', 'currencyId', 'iban']
    });

    // Ihrac
    if (eInvoiceTypeCode === 'IHRACKAYITLI') {
        invoice.items = invoice.items.map(item => {
            if (!!item.taxDetail && Array.isArray(item.taxDetail.applied)) {
                item.taxDetail.applied = item.taxDetail.applied.filter(appliedTax => !appliedTax.isStoppage);
            }

            return item;
        });
    }

    // Real totals.
    if (!!app.setting('eops.useRealTotalsForEInvoices')) {
        invoice.items = invoice.items.map(item => {
            item.unitPrice = round(item.realTotal / (item.quantity || 1), 4);
            item.unitPriceAfterDiscount = item.unitPrice;
            item.taxTotal = round(item.taxTotal - (item.taxTotal * item.discount) / 100, 2);
            if (!!item.taxDetail && Array.isArray(item.taxDetail.applied)) {
                item.taxDetail.applied = item.taxDetail.applied.map(appliedTax => {
                    appliedTax.appliedAmount = round(
                        appliedTax.appliedAmount - (appliedTax.appliedAmount * item.discount) / 100,
                        2
                    );

                    return appliedTax;
                });
            }
            item.discount = 0;
            item.discountAmount = 0;

            return item;
        });
        invoice.subTotal = invoice.subTotalAfterDiscount;
        invoice.subTotalAfterDiscount = invoice.subTotal;
        invoice.discount = 0;
        invoice.discountAmount = 0;
    }

    // Withholding taxes.
    const withHoldingTaxes = [];
    if (eInvoiceType.code === 'TEVKIFAT') {
        const condition = eInvoiceType.conditions.find(c => c.code === invoice.eInvoiceTypeConditionCode);

        invoice.appliedTaxes = invoice.appliedTaxes.filter(appliedTax => {
            if (appliedTax.isDeduction) {
                if (_.isNumber(condition.rate)) {
                    appliedTax.amount = condition.rate;
                }

                taxCodes[appliedTax._id] = condition.code;
                taxes[appliedTax._id] = {
                    ...(taxes[appliedTax._id] || {}),
                    label: condition.name
                };

                if (_.isString(taxCodes[appliedTax._id])) {
                    taxCodes[appliedTax._id] = taxCodes[appliedTax._id].trim();
                }

                appliedTax.additionalRate = taxes[appliedTax._id].additionalRate || '';

                withHoldingTaxes.push(appliedTax);

                return false;
            }

            return true;
        });

        invoice.items = invoice.items.map(item => {
            item.withHoldingTaxes = [];

            item.taxDetail.applied = item.taxDetail.applied.filter(appliedTax => {
                if (appliedTax.isDeduction) {
                    if (_.isNumber(condition.rate)) {
                        appliedTax.amount = condition.rate;
                    }

                    item.withHoldingTaxes.push(appliedTax);

                    return false;
                }

                return true;
            });

            return item;
        });
    }

    // Calculate totals.
    let taxInclusiveAmount = round(invoice.subTotal + invoice.taxTotal - invoice.discountAmount, currencyPrecision);
    let payableAmount = round(taxInclusiveAmount * scopeRate, currencyPrecision);
    let taxDeductionAmount = 0;
    for (const appliedTax of invoice.appliedTaxes) {
        if (appliedTax.appliedAmount < 0) {
            taxDeductionAmount += Math.abs(appliedTax.appliedAmount);
        }
    }
    for (const appliedTax of withHoldingTaxes) {
        if (appliedTax.appliedAmount < 0) {
            taxDeductionAmount += Math.abs(appliedTax.appliedAmount);
        }
    }
    taxInclusiveAmount += taxDeductionAmount;
    taxInclusiveAmount = round(taxInclusiveAmount * scopeRate, currencyPrecision);
    if (invoice.rounding !== 0) {
        payableAmount += round(invoice.rounding * scopeRate, currencyPrecision);
    }
    payableAmount = round(payableAmount, currencyPrecision);
    let subTotal = 0;
    let subTotalAfterDiscount = 0;
    let discount = 0;
    let discountAmount = invoice.discountAmount || 0;
    for (const item of invoice.items) {
        subTotal += round(item.unitPrice * item.quantity * scopeRate, currencyPrecision);
        discountAmount += round(
            ((item.discount || 0) * item.unitPrice * item.quantity * scopeRate) / 100,
            currencyPrecision
        );
    }
    subTotalAfterDiscount = subTotal - discountAmount;
    discount = subTotal > 0 ? (discountAmount / subTotal) * 100 : 0;
    subTotal = round(subTotal, currencyPrecision);
    subTotalAfterDiscount = round(subTotalAfterDiscount, currencyPrecision);
    discountAmount = round(discountAmount, currencyPrecision);
    discount = round(discount, 2);

    // Get note.
    const notes = [];
    let note = '';
    if (!!app.setting('eops.outputWrittenGrandTotalOnEInvoices')) {
        const currencyWrittenForms = {
            TRY: {main: 'TL', sub: 'KR'},
            USD: {main: 'USD', sub: 'SENT'},
            EUR: {main: 'EUR', sub: 'SENT'},
            GBP: {main: 'GBP', sub: 'PENİ'},
            RUB: {main: 'RUB', sub: 'KAPİK'},
            CHF: {main: 'CHF', sub: 'RAPPEN'}
        };

        const formatWrittenAmount = (amount, currencyCode) => {
            const currencyInfo = currencyWrittenForms[currencyCode] || {main: currencyCode, sub: 'SENT'};
            const hasSubUnit = Math.round(amount * 100) / 100 !== Math.floor(amount);
            let writtenAmount = '';

            if (hasSubUnit) {
                const parts = amount.toFixed(2).toString().split('.');
                const mainPart = parseInt(parts[0], 10);
                const subPart = parseInt(parts[1], 10);

                writtenAmount = `${toUpper(writtenNumber(mainPart, {lang: 'tr'})).replace(/\s/g, '')} ${toUpper(
                    currencyInfo.main
                )}`;
                if (subPart > 0) {
                    writtenAmount += ` ${toUpper(writtenNumber(subPart, {lang: 'tr'})).replace(/\s/g, '')} ${toUpper(
                        currencyInfo.sub
                    )}`;
                }
            } else {
                writtenAmount = `${toUpper(writtenNumber(amount, {lang: 'tr'})).replace(/\s/g, '')} ${toUpper(
                    currencyInfo.main
                )}`;
            }
            return `YALNIZ: ${writtenAmount}.`;
        };

        if (currencyName !== 'TRY') {
            note = formatWrittenAmount(payableAmount, currencyName);

            const tryPayableAmount = round(payableAmount * invoice.currencyRate, currencyPrecision);
            const tryNote = formatWrittenAmount(tryPayableAmount, 'TRY');
            notes.push(note);
            notes.push(tryNote);
        } else {
            note = formatWrittenAmount(payableAmount, 'TRY');
            notes.push(note);
        }
    }
    if (!!invoice.note) {
        notes.push(invoice.note.replace(/(?:\r\n|\r|\n)/g, '<br/>'));
    }
    if (
        !!app.setting('eops.outputWeightsOnEInvoices') &&
        (scenarioCode === 'YOLCUBERABERFATURA' || scenarioCode === 'IHRACAT')
    ) {
        let netWeight = 0;
        let grossWeight = 0;

        for (const item of invoice.items || []) {
            if (typeof item.netWeight === 'number' && !isNaN(item.netWeight)) {
                netWeight += item.netWeight;
            }
            if (typeof item.grossWeight === 'number' && !isNaN(item.grossWeight)) {
                grossWeight += item.grossWeight;
            }
        }
        if (netWeight > 0 && grossWeight > 0) {
            notes.push(`NET AĞIRLIK: ${app.format(netWeight, 'amount')}`);
            notes.push(`BRÜT AĞIRLIK: ${app.format(grossWeight, 'amount')}`);
        }
    }
    if (!!app.setting('eops.outputPartnerBalanceOnEInvoices')) {
        const partnerReport = await app.rpc('partners.get-partner-for-partner-select-component', invoice.partnerId);

        notes.push(`BAKİYE: ${app.format(partnerReport.balance, 'amount')} TL`);
    }
    if (!!app.setting('eops.outputDueDaysOnEInvoices')) {
        const dueDaysDiff = app.datetime
            .fromJSDate(invoice.dueDate)
            .diff(app.datetime.fromJSDate(invoice.issueDate))
            .as('days');

        notes.push(`VADE GÜN: ${Math.ceil(dueDaysDiff)}`);
    }
    // Kur bilgilerini her zaman göster (TL olduğunda da)
    if (currencyName === 'TRY' || currencyName === 'TL') {
        // Fatura para birimi TL ise, diğer tüm para birimlerinin kurlarını göster
        try {
            const allCurrencies = await app.collection('kernel.currencies').find({
                _id: {$ne: company.currencyId},
                isActive: true,
                $select: ['name']
            });

            const currencyPayloads = [];
            for (const currency of allCurrencies) {
                if (currency.name === 'TL' || currency.name === 'TRY') {
                    continue;
                }

                currencyPayloads.push({
                    from: 'TRY',
                    to: currency.name,
                    value: 1,
                    options: {
                        date: issueDate,
                        currencyPrecision: 4
                    }
                });
            }

            if (currencyPayloads.length > 0) {
                const exchangeRates = await app.rpc('kernel.common.convert-currencies', currencyPayloads);
                for (const rateInfo of exchangeRates) {
                    if (rateInfo.rate) {
                        notes.push(`${rateInfo.to}: ${round(rateInfo.rate, 4)}`);
                    }
                }
            }
        } catch (error) {
            // Kur bilgisi alınamadığında sessizce devam et
        }
    } else {
        // Fatura para birimi TL değilse, sadece o para biriminin kurunu göster
        notes.push(`${currencyName}: ${invoice.currencyRate}`);
    }
    if (eInvoiceTypeCode === 'IHRACKAYITLI' && invoice.eInvoiceTypeConditionCode) {
        const condition = eInvoiceType.conditions.find(c => c.code === invoice.eInvoiceTypeConditionCode);

        if (!!condition) {
            notes.push(`${condition.code} ${condition.name}`);
        }
    }
    if (app.setting('eops.showProjectInNotesOnEInvoices') && invoice.financialProjectId) {
        const financialProject = await app.collection('kernel.financial-projects').findOne({
            _id: invoice.financialProjectId,
            $select: ['code', 'name'],
            $disableActiveCheck: true,
            $disableSoftDelete: true
        });

        if (financialProject) {
            notes.push(`PROJE: ${financialProject.code} - ${financialProject.name}`);
        }
    }
    if (app.setting('eops.showDeliveryAddressCodeInNotesOnEInvoices') && invoice.deliveryAddressId) {
        const contact = await app.collection('kernel.contacts').findOne({
            _id: invoice.deliveryAddressId,
            $select: ['code'],
            $disableActiveCheck: true,
            $disableSoftDelete: true
        });

        if (contact && contact.code) {
            notes.push(`TESLİMAT ADRESİ KODU: ${contact.code}`);
        }
    }

    // Check if we have all the required tax codes.
    for (const appliedTax of invoice.appliedTaxes) {
        if (!taxCodes[appliedTax._id]) {
            throw new app.errors.Unprocessable(
                app.translate('Could not found the tax code for {{tax}}!', {
                    tax: appliedTax.label
                })
            );
        }
    }

    // Check if we have all the required unit codes.
    for (const item of invoice.items) {
        if (!unitCodes[item.unitId]) {
            const unit = await app.collection('kernel.units').findOne({
                _id: item.unitId,
                $select: ['name'],
                $disableActiveCheck: true,
                $disableSoftDelete: true
            });

            throw new app.errors.Unprocessable(
                app.translate('Could not found the unit code for {{unit}}!', {
                    unit: unit.name
                })
            );
        }
    }

    // Check supplier address.
    if (
        !supplier.address.countryId ||
        supplier.address.countryId === '' ||
        !supplier.address.city ||
        supplier.address.city === '' ||
        !supplier.address.district ||
        supplier.address.district === ''
        // !supplier.address.subDistrict ||
        // supplier.address.subDistrict === '' ||
        // !supplier.address.street ||
        // supplier.address.street === ''
        // !supplier.address.city ||
        // supplier.address.city === '' ||
        // !supplier.address.district ||
        // supplier.address.district === '' ||
        // !supplier.address.subDistrict ||
        // supplier.address.subDistrict === '' ||
        // !supplier.address.street ||
        // supplier.address.street === ''
    ) {
        throw new app.errors.Unprocessable(app.translate('Supplier address is invalid!'));
    }

    // Check customer address.
    if (
        !customer.address.countryId ||
        customer.address.countryId === '' ||
        !customer.address.city ||
        customer.address.city === '' ||
        !customer.address.district ||
        customer.address.district === ''
        // !customer.address.city ||
        // customer.address.city === '' ||
        // !customer.address.district ||
        // customer.address.district === '' ||
        // !customer.address.subDistrict ||
        // customer.address.subDistrict === '' ||
        // !customer.address.street ||
        // customer.address.street === ''
    ) {
        throw new app.errors.Unprocessable(app.translate('Customer address is invalid!'));
    }

    // Order info.
    let orderInfo = null;

    // Waybill info.
    let lotNumbers = [];
    let waybillInfos = [];
    if (!!invoice.transferIds && Array.isArray(invoice.transferIds) && invoice.transferIds.length > 0) {
        const transfers = await app.collection('inventory.transfers').find({
            _id: {$in: invoice.transferIds},
            $select: ['_id', 'issueDate', 'scheduledDate', 'documentNo', 'referenceId', 'referenceCollection', 'items'],
            $disableSoftDelete: true
        });

        for (const transfer of transfers) {
            const eWaybill = await app.collection('logistics.logistics').findOne({
                waybillStatus: {$ne: 'draft'},
                transferId: transfer._id,
                $select: ['waybillDocumentNo', 'issueDate', 'waybillSendingDate', 'deliveryDate'],
                $disableSoftDelete: true
            });
            let waybillInfo = {};

            if (!!transfer.referenceId && !!transfer.referenceCollection) {
                const order = await app.collection(transfer.referenceCollection).findOne({
                    _id: transfer.referenceId,
                    $select: ['code', 'orderDate']
                });

                if (orderInfo === null && !!order && _.isDate(order.orderDate)) {
                    orderInfo = {
                        code: order.code,
                        date: order.orderDate
                    };
                }
            }

            if (!!eWaybill && !!eWaybill.waybillDocumentNo && _.isDate(eWaybill.issueDate)) {
                waybillInfo = {
                    id: eWaybill.waybillDocumentNo,
                    issueDate: eWaybill.issueDate
                };

                waybillInfos.push(waybillInfo);
            }

            // else if (!!eWaybill && _.isDate(eWaybill.deliveryDate)) {
            //     waybillInfo = {
            //         id: transfer.documentNo || '',
            //         issueDate: eWaybill.deliveryDate
            //     };
            // } else {
            //     waybillInfo = {
            //         id: transfer.documentNo || '',
            //         issueDate: transfer.scheduledDate
            //     };
            // }

            for (const item of transfer.items ?? []) {
                const subItems = item.subItems ?? [];

                for (const subItem of subItems) {
                    if (!!subItem.lotNumber) {
                        lotNumbers.push({
                            productId: item.productId,
                            lotNumber: subItem.lotNumber,
                            quantity: subItem.quantity || 0
                        });
                    }
                }
            }
        }
    }
    // waybillInfos = waybillInfos.map(waybillInfo => {
    //     if (
    //         !!waybillInfo &&
    //         _.isDate(waybillInfo.issueDate) &&
    //         _.isDate(issueDate) &&
    //         waybillInfo.issueDate.getTime() > issueDate.getTime()
    //     ) {
    //         waybillInfo.issueDate = issueDate;
    //     }
    //
    //     return waybillInfo;
    // });

    if (
        !!orderInfo &&
        _.isDate(orderInfo.date) &&
        _.isDate(issueDate) &&
        orderInfo.date.getTime() > issueDate.getTime()
    ) {
        orderInfo.date = issueDate;
    }

    // Lot numbers.
    if (lotNumbers.length > 0) {
        const lots = await app.collection('inventory.lot-numbers').find({
            lotNumber: {$in: _.uniq(lotNumbers.map(n => n.lotNumber))}
        });
        const grouped = _.groupBy(lotNumbers, 'productId');
        const items = [];

        for (const productId of Object.keys(grouped)) {
            const index = items.findIndex(item => item.productId === productId);

            if (index === -1) {
                const item = {};

                item.productId = productId;
                item.lotNumbers = _.uniq(
                    lotNumbers.filter(ln => ln.productId === productId).map(ln => ln.lotNumber)
                ).map(ln => {
                    const lot = lots.find(lot => lot.lotNumber === ln);
                    const n = {};

                    n.lotNumber = lot.lotNumber;
                    n.expirationDate = _.isDate(lot.expirationDate) ? app.format(lot.expirationDate, 'date') : '';
                    n.quantity = 0;

                    for (const lotNumber of lotNumbers) {
                        if (lotNumber.productId === item.productId && lotNumber.lotNumber === n.lotNumber) {
                            n.quantity += lotNumber.quantity || 0;
                        }
                    }

                    return n;
                });

                items.push(item);
            }
        }

        lotNumbers = items;
    }

    if (!orderInfo && Array.isArray(invoice.relatedDocuments)) {
        const rd = invoice.relatedDocuments.find(rd => rd.collection === 'sale.orders');

        if (!!rd && Array.isArray(rd.ids) && rd.ids.length > 0) {
            const order = await app.collection('sale.orders').findOne({
                _id: rd.ids[0],
                $select: ['code', 'orderDate']
            });

            if (!!order && _.isDate(order.orderDate)) {
                orderInfo = {
                    code: order.code,
                    date: order.orderDate
                };
            }
        }
    }

    let deliveryCondition = null;
    if (!!invoice.deliveryConditionId) {
        deliveryCondition = await app.collection('logistics.delivery-conditions').findOne({
            _id: invoice.deliveryConditionId,
            $disableActiveCheck: true,
            $disableSoftDelete: true
        });
    }

    // Invoice design.
    let design = null;
    if (!!app.setting('eops.useSystemDesignsForEInvoices')) {
        const template = !!templateId
            ? await app.collection('eops.templates').findOne({
                  _id: templateId
              })
            : await app.collection('eops.templates').findOne({
                  type: eInvoice.invoiceScenario
              });

        if (!!template) {
            template.content = template.content.replace(
                '{{dueDate}}',
                app.datetime.fromJSDate(invoice.dueDate).startOf('day').toFormat('dd-LL-yyyy')
            );

            design = Buffer.from(template.content).toString('base64');
        }
    }

    // Order reference.
    let orderReference = null;
    if (!!invoice.partnerOrderReference) {
        orderReference = {
            'cbc:ID': invoice.partnerOrderReference,
            'cbc:IssueDate': _.isDate(invoice.partnerOrderDate)
                ? app.datetime.fromJSDate(invoice.partnerOrderDate).startOf('day').toFormat('yyyy-LL-dd')
                : app.datetime.fromJSDate(issueDate).startOf('day').toFormat('yyyy-LL-dd')
        };
    } else {
        orderReference = !!orderInfo
            ? {
                  'cbc:ID': orderInfo.code,
                  'cbc:IssueDate': app.datetime.fromJSDate(orderInfo.date).startOf('day').toFormat('yyyy-LL-dd')
              }
            : {
                  'cbc:ID': invoice.code,
                  'cbc:IssueDate': app.datetime.fromJSDate(issueDate).startOf('day').toFormat('yyyy-LL-dd')
              };
    }

    // Returned invoice reference.
    let returnedInvoiceReference = null;
    if (invoice.isReturn && eInvoiceTypeCode === 'IADE') {
        if (!!invoice.returnedInvoiceDocumentNo && _.isDate(invoice.returnedInvoiceIssueDate)) {
            returnedInvoiceReference = {
                'cbc:ID': invoice.returnedInvoiceDocumentNo,
                'cbc:IssueDate': app.datetime
                    .fromJSDate(invoice.returnedInvoiceIssueDate)
                    .startOf('day')
                    .toFormat('yyyy-LL-dd'),
                'cbc:DocumentTypeCode': 'IADE',
                'cbc:DocumentType': 'İade Edilen Fatura'
            };
        } else {
            throw new app.errors.Unprocessable(
                app.translate('Returned invoice information is incorrect or incomplete!')
            );
        }
    } else if (
        eInvoiceTypeCode === 'IADE' &&
        !!invoice.returnedInvoiceDocumentNo &&
        _.isDate(invoice.returnedInvoiceIssueDate)
    ) {
        returnedInvoiceReference = {
            'cbc:ID': invoice.returnedInvoiceDocumentNo,
            'cbc:IssueDate': app.datetime
                .fromJSDate(invoice.returnedInvoiceIssueDate)
                .startOf('day')
                .toFormat('yyyy-LL-dd'),
            'cbc:DocumentTypeCode': 'IADE',
            'cbc:DocumentType': 'İade Edilen Fatura'
        };
    }

    // Invoice xml object.
    const invoiceObj = {
        Invoice: {
            $: {
                'xmlns:ds': 'http://www.w3.org/2000/09/xmldsig#',
                'xmlns:xsi': 'http://www.w3.org/2001/XMLSchema-instance',
                'xmlns:qdt': 'urn:oasis:names:specification:ubl:schema:xsd:QualifiedDatatypes-2',
                'xmlns:ext': 'urn:oasis:names:specification:ubl:schema:xsd:CommonExtensionComponents-2',
                'xmlns:udt': 'urn:un:unece:uncefact:data:specification:UnqualifiedDataTypesSchemaModule:2',
                'xmlns:xades': 'http://uri.etsi.org/01903/v1.3.2#',
                'xmlns:ubltr': 'urn:oasis:names:specification:ubl:schema:xsd:TurkishCustomizationExtensionComponents',
                'xmlns:cac': 'urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2',
                'xmlns:cbc': 'urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2',
                'xmlns:ccts': 'urn:un:unece:uncefact:documentation:2',
                xmlns: 'urn:oasis:names:specification:ubl:schema:xsd:Invoice-2'
            }
        }
    };

    // Invoice general.
    invoiceObj.Invoice = {
        ...invoiceObj.Invoice,
        'ext:UBLExtensions': {'ext:UBLExtension': {'ext:ExtensionContent': {}}},
        'cbc:UBLVersionID': '2.1',
        'cbc:CustomizationID': 'TR1.2',
        'cbc:CopyIndicator': false,
        'cbc:ID': invoiceDocumentNo,
        'cbc:ProfileID': scenarioCode,
        'cbc:UUID': invoiceUUID,
        'cbc:IssueDate': app.datetime.fromJSDate(issueDate).startOf('day').toFormat('yyyy-LL-dd'),
        'cbc:IssueTime': app.datetime.fromJSDate(issueDate).toFormat('HH:mm:ss'),
        'cbc:InvoiceTypeCode': eInvoiceTypeCode,
        'cbc:Note': notes,
        'cbc:DocumentCurrencyCode': currencyName,
        'cbc:LineCountNumeric': invoice.items.length,
        ...(!!returnedInvoiceReference
            ? {
                  'cac:BillingReference': {
                      'cac:InvoiceDocumentReference': returnedInvoiceReference
                  }
              }
            : {}),
        ...(!returnedInvoiceReference ? {'cac:OrderReference': orderReference} : {}),
        ...(waybillInfos.length > 0
            ? {
                  'cac:DespatchDocumentReference': waybillInfos.map(waybillInfo => ({
                      'cbc:ID': waybillInfo.id,
                      'cbc:IssueDate': app.datetime
                          .fromJSDate(waybillInfo.issueDate)
                          .startOf('day')
                          .toFormat('yyyy-LL-dd')
                  }))
              }
            : {}),
        'cac:AdditionalDocumentReference': [
            {
                'cbc:ID': uuid(),
                'cbc:IssueDate': app.datetime.fromJSDate(issueDate).startOf('day').toFormat('yyyy-LL-dd'),
                'cbc:DocumentTypeCode': 'CUST_INV_ID'
            },
            ...(!!design
                ? [
                      {
                          'cbc:ID': invoiceDocumentNo,
                          'cbc:IssueDate': app.datetime.fromJSDate(issueDate).startOf('day').toFormat('yyyy-LL-dd'),
                          'cbc:DocumentType': 'XSLT',
                          'cbc:DocumentTypeCode': 'XSLT',
                          'cac:Attachment': {
                              'cbc:EmbeddedDocumentBinaryObject': {
                                  $: {
                                      characterSetCode: 'UTF-8',
                                      encodingCode: 'Base64',
                                      filename: `invoiceDocumentNo.xslt`,
                                      mimeCode: 'application/xml'
                                  },
                                  _: design
                              }
                          }
                      }
                  ]
                : [])
        ]
    };

    // Signature
    invoiceObj.Invoice = {
        ...invoiceObj.Invoice,
        'cac:Signature': {
            'cbc:ID': {
                $: {
                    schemeID: 'VKN_TCKN'
                },
                _: company.tin
            },
            'cac:SignatoryParty': {
                'cbc:WebsiteURI': company.website,
                'cac:PartyIdentification': {
                    'cbc:ID': {
                        $: {
                            schemeID: (company.tin || '').length === 11 ? 'TCKN' : 'VKN'
                        },
                        _: company.tin
                    }
                },
                'cac:PartyName': {
                    'cbc:Name': company.legalName
                },
                'cac:PostalAddress': {
                    'cbc:Room': company.address.doorNumber,
                    'cbc:StreetName': company.address.street,
                    'cbc:BuildingNumber': company.address.apartmentNumber,
                    'cbc:CitySubdivisionName': company.address.district,
                    'cbc:CityName': company.address.city,
                    'cbc:PostalZone': company.address.postalCode,
                    'cac:Country': {
                        'cbc:Name': countryNames[company.address.countryId]
                    }
                },
                'cac:Contact': {
                    'cbc:Telephone': company.phone,
                    'cbc:ElectronicMail': company.email
                },
                ...((company.tin || '').length === 11
                    ? {
                          'cac:Person': {
                              'cbc:FirstName': company.firstName,
                              'cbc:FamilyName': company.lastName
                          }
                      }
                    : {})
            },
            'cac:DigitalSignatureAttachment': {
                'cac:ExternalReference': {
                    'cbc:URI': '#Signature'
                }
            }
        }
    };

    // Supplier.
    invoiceObj.Invoice = {
        ...invoiceObj.Invoice,
        'cac:AccountingSupplierParty': {
            'cac:Party': {
                'cbc:WebsiteURI': supplier.website,
                ...((_.isString(mersisNo) && mersisNo.length > 0) ||
                (_.isString(ticaretSicilNo) && ticaretSicilNo.length > 0)
                    ? {
                          'cac:PartyIdentification': [
                              {
                                  'cbc:ID': {
                                      $: {
                                          schemeID: (company.tin || '').length === 11 ? 'TCKN' : 'VKN'
                                      },
                                      _: company.tin
                                  }
                              },
                              {
                                  'cbc:ID': {
                                      $: {
                                          schemeID: 'MERSISNO'
                                      },
                                      _: !!mersisNo ? mersisNo : ''
                                  }
                              },
                              {
                                  'cbc:ID': {
                                      $: {
                                          schemeID: 'TICARETSICILNO'
                                      },
                                      _: !!ticaretSicilNo ? ticaretSicilNo : ''
                                  }
                              }
                          ]
                      }
                    : {
                          'cac:PartyIdentification': {
                              'cbc:ID': {
                                  $: {
                                      schemeID: (company.tin || '').length === 11 ? 'TCKN' : 'VKN'
                                  },
                                  _: company.tin
                              }
                          }
                      }),
                'cac:PartyName': {
                    'cbc:Name': supplier.legalName
                },
                'cac:PostalAddress': {
                    'cbc:Room': supplier.address.doorNumber,
                    'cbc:StreetName': `${supplier.address.subDistrict} ${supplier.address.street}`,
                    'cbc:BuildingName': '',
                    'cbc:BuildingNumber': supplier.address.apartmentNumber,
                    'cbc:CitySubdivisionName': supplier.address.district,
                    'cbc:CityName': supplier.address.city,
                    'cbc:PostalZone': supplier.address.postalCode,
                    'cbc:Region': '',
                    // 'cbc:District': supplier.address.subDistrict,
                    'cac:Country': {
                        'cbc:Name': countryNames[supplier.address.countryId]
                    }
                },
                ...(!!supplier.taxDepartment
                    ? {
                          'cac:PartyTaxScheme': {
                              'cac:TaxScheme': {
                                  'cbc:Name': supplier.taxDepartment || '',
                                  'cbc:TaxTypeCode': '0'
                              }
                          }
                      }
                    : {}),
                'cac:Contact': {
                    'cbc:Telephone': supplier.phone,
                    'cbc:Telefax': '',
                    'cbc:ElectronicMail': supplier.email
                },
                ...((company.tin || '').length === 11
                    ? {
                          'cac:Person': {
                              'cbc:FirstName': company.firstName,
                              'cbc:FamilyName': company.lastName
                          }
                      }
                    : {})
            }
        }
    };

    // Customer.
    invoiceObj.Invoice = {
        ...invoiceObj.Invoice,
        'cac:AccountingCustomerParty': {
            'cac:Party': {
                'cbc:WebsiteURI': customer.website,
                ...(customer.isCompany
                    ? {
                          'cac:PartyIdentification': {
                              'cbc:ID': {
                                  $: {
                                      schemeID: 'VKN'
                                  },
                                  _: (customer.tin ?? '').slice(0, 10)
                              }
                          },
                          'cac:PartyName': {
                              'cbc:Name': customer.legalName
                          }
                      }
                    : {
                          'cac:PartyIdentification': {
                              'cbc:ID': {
                                  $: {
                                      schemeID: 'TCKN'
                                  },
                                  _: (customer.identity ?? '').slice(0, 11)
                              }
                          }
                      }),
                'cac:PostalAddress': {
                    'cbc:Room': customer.address.doorNumber,
                    'cbc:StreetName': `${customer.address.subDistrict} ${customer.address.street}`,
                    'cbc:BuildingName': '',
                    'cbc:BuildingNumber': customer.address.apartmentNumber,
                    'cbc:CitySubdivisionName': customer.address.district,
                    'cbc:CityName': customer.address.city,
                    'cbc:PostalZone': customer.address.postalCode,
                    'cbc:Region': '',
                    // 'cbc:District': customer.address.subDistrict,
                    'cac:Country': {
                        'cbc:Name': countryNames[customer.address.countryId]
                    }
                },
                ...(!!customer.taxDepartment
                    ? {
                          'cac:PartyTaxScheme': {
                              'cac:TaxScheme': {
                                  'cbc:Name': customer.taxDepartment || '',
                                  'cbc:TaxTypeCode': '0'
                              }
                          }
                      }
                    : {}),
                'cac:Contact': {
                    'cbc:Telephone': customer.phone,
                    'cbc:Telefax': '',
                    'cbc:ElectronicMail': customer.email
                },
                ...(!customer.isCompany
                    ? {
                          'cac:Person': {
                              'cbc:FirstName': customer.firstName,
                              'cbc:FamilyName': customer.lastName,
                              'cbc:MiddleName': ''
                          }
                      }
                    : {})
            }
        }
    };

    if (scenarioCode === 'YOLCUBERABERFATURA') {
        const country = countries.find(country => country._id === customer.address.countryId);

        invoiceObj.Invoice = {
            ...invoiceObj.Invoice,
            'cac:AccountingCustomerParty': {
                'cac:Party': {
                    'cbc:WebsiteURI': '',
                    'cac:PartyIdentification': {
                        'cbc:ID': {
                            $: {
                                schemeID: 'VKN'
                            },
                            _: '**********'
                        }
                    },
                    'cac:PartyName': {
                        'cbc:Name': 'Gümrük ve Ticaret Bakanlığı'
                    },
                    'cac:PostalAddress': {
                        'cbc:Room': '',
                        'cbc:StreetName': 'Üniversiteler Mahallesi Dumlupınar Bulvarı',
                        'cbc:BuildingName': '',
                        'cbc:BuildingNumber': '151',
                        'cbc:CitySubdivisionName': 'Çankaya',
                        'cbc:CityName': 'ANKARA',
                        'cbc:PostalZone': '',
                        'cbc:Region': '',
                        'cac:Country': {
                            'cbc:Name': 'TÜRKİYE'
                        }
                    },
                    'cac:PartyTaxScheme': {
                        'cac:TaxScheme': {
                            'cbc:Name': 'ULUS VERGİ DAİRESİ (6260)'
                        }
                    },
                    'cac:Contact': {
                        'cbc:Telephone': '',
                        'cbc:Telefax': '',
                        'cbc:ElectronicMail': ''
                    }
                }
            },
            'cac:BuyerCustomerParty': {
                'cac:Party': {
                    'cbc:WebsiteURI': customer.website,
                    'cac:PartyIdentification': {
                        'cbc:ID': {
                            $: {
                                schemeID: 'PARTYTYPE'
                            },
                            _: 'TAXFREE'
                        }
                    },
                    'cac:PostalAddress': {
                        'cbc:Room': customer.address.doorNumber,
                        'cbc:StreetName': `${customer.address.subDistrict} ${customer.address.street}`,
                        'cbc:BuildingName': '',
                        'cbc:BuildingNumber': customer.address.apartmentNumber,
                        'cbc:CitySubdivisionName': customer.address.district,
                        'cbc:CityName': customer.address.city,
                        'cbc:PostalZone': customer.address.postalCode,
                        'cbc:Region': '',
                        'cac:Country': {
                            'cbc:Name': countryNames[customer.address.countryId]
                        }
                    },
                    'cac:Contact': {
                        'cbc:Telephone': customer.phone,
                        'cbc:Telefax': '',
                        'cbc:ElectronicMail': customer.email
                    },
                    'cac:Person': {
                        'cbc:FirstName': customer.firstName,
                        'cbc:FamilyName': customer.lastName,
                        'cbc:MiddleName': '',
                        'cbc:NationalityID': country.code,
                        'cac:IdentityDocumentReference': {
                            'cbc:ID': customer.passportNo,
                            'cbc:IssueDate': app.datetime.fromJSDate(issueDate).startOf('day').toFormat('yyyy-LL-dd')
                        }
                    }
                }
            },
            'cac:TaxRepresentativeParty': {
                'cbc:WebsiteURI': '',
                'cac:PartyIdentification': [
                    {
                        'cbc:ID': {
                            $: {
                                schemeID: 'ARACIKURUMVKN'
                            },
                            _: '**********'
                        }
                    },
                    {
                        'cbc:ID': {
                            $: {
                                schemeID: 'ARACIKURUMETIKET'
                            },
                            _: 'urn:mail:<EMAIL>'
                        }
                    }
                ],
                'cac:PartyName': {
                    'cbc:Name': 'Ticaret Bakanlığı - Bilgi Teknolojileri Genel Müdürlüğü'
                },
                'cac:PostalAddress': {
                    'cbc:Room': '',
                    'cbc:StreetName': '',
                    'cbc:BuildingName': '',
                    'cbc:BuildingNumber': '',
                    'cbc:CitySubdivisionName': '',
                    'cbc:CityName': '',
                    'cbc:PostalZone': '',
                    'cbc:Region': '',
                    'cac:Country': {
                        'cbc:Name': ''
                    }
                }
            },
            'cac:Delivery': {
                'cac:Shipment': {
                    'cbc:ID': ''
                    // 'cbc:DeclaredCustomsValueAmount': {
                    //     $: {
                    //         currencyID: currencyName
                    //     },
                    //     _: payableAmount
                    // }
                }
            }
        };
    }

    if (scenarioCode === 'IHRACAT') {
        invoiceObj.Invoice = {
            ...invoiceObj.Invoice,
            'cac:AccountingCustomerParty': {
                'cac:Party': {
                    'cbc:WebsiteURI': '',
                    'cac:PartyIdentification': {
                        'cbc:ID': {
                            $: {
                                schemeID: 'VKN'
                            },
                            _: '**********'
                        }
                    },
                    'cac:PartyName': {
                        'cbc:Name': 'Gümrük ve Ticaret Bakanlığı'
                    },
                    'cac:PostalAddress': {
                        'cbc:Room': '',
                        'cbc:StreetName': 'Üniversiteler Mahallesi Dumlupınar Bulvarı',
                        'cbc:BuildingName': '',
                        'cbc:BuildingNumber': '151',
                        'cbc:CitySubdivisionName': 'Çankaya',
                        'cbc:CityName': 'ANKARA',
                        'cbc:PostalZone': '',
                        'cbc:Region': '',
                        'cac:Country': {
                            'cbc:Name': 'TÜRKİYE'
                        }
                    },
                    'cac:PartyTaxScheme': {
                        'cac:TaxScheme': {
                            'cbc:Name': 'ULUS VERGİ DAİRESİ (6260)'
                        }
                    },
                    'cac:Contact': {
                        'cbc:Telephone': '',
                        'cbc:Telefax': '',
                        'cbc:ElectronicMail': ''
                    }
                }
            },
            'cac:BuyerCustomerParty': {
                'cac:Party': {
                    'cbc:WebsiteURI': customer.website,
                    'cac:PartyIdentification': {
                        'cbc:ID': {
                            $: {
                                schemeID: 'PARTYTYPE'
                            },
                            _: 'EXPORT'
                        }
                    },
                    'cac:PartyName': {
                        'cbc:Name': customer.isCompany ? customer.legalName : customer.name
                    },
                    'cac:PostalAddress': {
                        'cbc:Room': customer.address.doorNumber,
                        'cbc:StreetName': `${customer.address.subDistrict} ${customer.address.street}`,
                        'cbc:BuildingName': '',
                        'cbc:BuildingNumber': customer.address.apartmentNumber,
                        'cbc:CitySubdivisionName': customer.address.district,
                        'cbc:CityName': customer.address.city,
                        'cbc:PostalZone': customer.address.postalCode,
                        'cbc:Region': '',
                        'cac:Country': {
                            'cbc:Name': countryNames[customer.address.countryId]
                        }
                    },
                    'cac:PartyLegalEntity': {
                        ...(customer.isCompany
                            ? {
                                  'cbc:RegistrationName': customer.tin,
                                  'cbc:CompanyID': customer.legalName
                              }
                            : {
                                  'cbc:RegistrationName': customer.identity,
                                  'cbc:CompanyID': customer.name
                              })
                    },
                    'cac:Contact': {
                        'cbc:Telephone': customer.phone,
                        'cbc:Telefax': '',
                        'cbc:ElectronicMail': customer.email
                    }
                }
            },
            'cac:Delivery': {
                'cac:Shipment': {
                    'cbc:ID': ''
                    // 'cbc:DeclaredCustomsValueAmount': {
                    //     $: {
                    //         currencyID: currencyName
                    //     },
                    //     _: payableAmount
                    // }
                }
            }
        };
    }

    // Legal Customer.
    if (scenarioCode === 'KAMU' && !!invoice.paymentAddressId) {
        const contact = await app.collection('kernel.contacts').findOne({
            _id: invoice.paymentAddressId,
            $disableActiveCheck: true,
            $disableSoftDelete: true
        });

        if (!!contact && contact.invoiceType === 'corporate') {
            invoiceObj.Invoice = {
                ...invoiceObj.Invoice,
                'cac:BuyerCustomerParty': {
                    'cac:Party': {
                        'cac:PartyIdentification': {
                            'cbc:ID': {
                                $: {
                                    schemeID: 'VKN'
                                },
                                _: contact.tin
                            }
                        },
                        'cac:PartyName': {
                            'cbc:Name': contact.companyName
                        },
                        'cac:PostalAddress': {
                            'cbc:Room': contact.address.doorNumber,
                            'cbc:StreetName': `${contact.address.subDistrict} ${contact.address.street}`,
                            'cbc:BuildingName': '',
                            'cbc:BuildingNumber': contact.address.apartmentNumber,
                            'cbc:CitySubdivisionName': contact.address.district,
                            'cbc:CityName': contact.address.city,
                            'cbc:PostalZone': contact.address.postalCode,
                            'cbc:Region': '',
                            'cac:Country': {
                                'cbc:Name': countryNames[contact.address.countryId]
                            }
                        }
                    }
                }
            };
        }
    } else if (!!customer.isGovernmentOffice && !!customer.isCompany) {
        invoiceObj.Invoice = {
            ...invoiceObj.Invoice,
            'cac:BuyerCustomerParty': {
                'cac:Party': {
                    'cbc:WebsiteURI': customer.website,
                    'cac:PartyIdentification': [
                        {
                            'cbc:ID': {
                                $: {
                                    schemeID: 'VKN'
                                },
                                _: customer.tin
                            }
                        },
                        {
                            'cbc:ID': {
                                $: {
                                    schemeID: 'PARTYTYPE'
                                },
                                _: 'KAMU'
                            }
                        }
                    ],
                    'cac:PartyName': {
                        'cbc:Name': customer.legalName
                    },
                    'cac:PostalAddress': {
                        'cbc:Room': customer.address.doorNumber,
                        'cbc:StreetName': `${customer.address.subDistrict} ${customer.address.street}`,
                        'cbc:BuildingName': '',
                        'cbc:BuildingNumber': customer.address.apartmentNumber,
                        'cbc:CitySubdivisionName': customer.address.district,
                        'cbc:CityName': customer.address.city,
                        'cbc:PostalZone': customer.address.postalCode,
                        'cbc:Region': '',
                        // 'cbc:District': customer.address.subDistrict,
                        'cac:Country': {
                            'cbc:Name': countryNames[customer.address.countryId]
                        }
                    },
                    'cac:PartyLegalEntity': {
                        'cbc:RegistrationName': customer.legalName,
                        'cbc:CompanyID': customer.tin
                    }
                }
            }
        };
    }

    // Payment Info.
    invoiceObj.Invoice = {
        ...invoiceObj.Invoice,
        'cac:PaymentMeans': {
            'cbc:PaymentMeansCode': '30',
            'cbc:PaymentDueDate': app.datetime
                .fromJSDate(invoice.dueDate || issueDate)
                .startOf('day')
                .toFormat('yyyy-LL-dd'),
            'cac:PayeeFinancialAccount': (bankAccounts || []).map(bankAccount => {
                return {
                    'cbc:ID': bankAccount.iban,
                    'cbc:CurrencyCode': currencyName,
                    'cbc:PaymentNote': bankAccount.name
                };
            }),
            'cbc:CurrencyCode': currencyName
        }
    };
    if (!!invoice.paymentTerm) {
        invoiceObj.Invoice = {
            ...invoiceObj.Invoice,
            'cac:PaymentTerms': {
                'cbc:Note': invoice.paymentTerm.name
            }
        };
    }

    // Items.
    const lineObjs = [];
    let index = 0;
    for (const item of invoice.items) {
        const product = productsMap[item.productId];
        let originCountry = null;
        if (!!item.countryOfOriginId) {
            originCountry = countries.find(country => country._id === item.countryOfOriginId);
        }

        if (lotNumbers.length > 0 && app.setting('eops.outputLotSerialNumbersOnEInvoiceItems')) {
            const lineLot = lotNumbers.find(ln => ln.productId === item.productId);

            if (!!lineLot) {
                item.description = `${item.description}<br/>${lineLot.lotNumbers
                    .map(
                        ln =>
                            `LN: ${ln.lotNumber}${!!ln.expirationDate ? `, SKT: ${ln.expirationDate}` : ''}, MİK: ${
                                ln.quantity
                            }`
                    )
                    .join('<br/>')}`;
            }
        }

        let supplierCatalogNo = null;
        if (!!item.vendorId) {
            const procurementItem = await app.collection('purchase.procurement-catalog').findOne({
                productId: item.productId,
                vendorId: item.vendorId,
                $select: ['supplierCatalogNo']
            });

            if (_.isPlainObject(procurementItem) && !!procurementItem.supplierCatalogNo) {
                supplierCatalogNo = procurementItem.supplierCatalogNo;
            }
        }

        let lineObj = {
            'cbc:ID': index + 1,
            'cbc:InvoicedQuantity': {
                $: {
                    unitCode: unitCodes[item.unitId].trim()
                },
                _: item.quantity
            },
            'cbc:LineExtensionAmount': {
                $: {
                    currencyID: currencyName
                },
                _: round(item.unitPrice * item.quantity * scopeRate, currencyPrecision)
            },
            'cac:AllowanceCharge': {
                'cbc:ChargeIndicator': false,
                'cbc:MultiplierFactorNumeric': round(item.discount / 100, 4),
                'cbc:Amount': {
                    $: {
                        currencyID: currencyName
                    },
                    _: round(((item.unitPrice * item.quantity * item.discount) / 100) * scopeRate, currencyPrecision)
                },
                'cbc:BaseAmount': {
                    $: {
                        currencyID: currencyName
                    },
                    _: round(item.unitPrice * item.quantity * scopeRate, currencyPrecision)
                }
            },
            'cac:TaxTotal': {
                'cbc:TaxAmount': {
                    $: {
                        currencyID: currencyName
                    },
                    _: round(
                        _.sum(
                            ((item.taxDetail || {}).applied || []).map(appliedTax => Math.abs(appliedTax.appliedAmount))
                        ) * scopeRate,
                        currencyPrecision
                    )
                },
                'cac:TaxSubtotal': groupedTaxes((item.taxDetail || {}).applied || []).map((appliedTax, index) => {
                    return {
                        ...(appliedTax.computation === 'percent'
                            ? {
                                  'cbc:TaxableAmount': {
                                      $: {currencyID: currencyName},
                                      _:
                                          appliedTax.unAppliedAmount > 0
                                              ? Math.abs(
                                                    round(appliedTax.unAppliedAmount * scopeRate, currencyPrecision)
                                                )
                                              : 0
                                  }
                              }
                            : {}),
                        'cbc:TaxAmount': {
                            $: {
                                currencyID: currencyName
                            },
                            _: round(Math.abs(appliedTax.appliedAmount) * scopeRate, currencyPrecision)
                        },
                        ...(appliedTax.computation === 'percent' ? {'cbc:Percent': appliedTax.amount} : {}),
                        'cbc:CalculationSequenceNumeric': index + 1,
                        'cac:TaxCategory': {
                            ...(!!exemptionReasonCode
                                ? {
                                      'cbc:TaxExemptionReasonCode': exemptionReasonCode,
                                      'cbc:TaxExemptionReason': exemptionReason
                                  }
                                : {}),
                            'cac:TaxScheme': {
                                'cbc:Name': taxes[appliedTax._id].label
                                    ? taxes[appliedTax._id].label
                                    : appliedTax.label,
                                'cbc:TaxTypeCode': taxCodes[appliedTax._id]
                            }
                        }
                    };
                })
            },
            ...(Array.isArray(item.withHoldingTaxes) && item.withHoldingTaxes.length > 0
                ? {
                      'cac:WithholdingTaxTotal': {
                          'cbc:TaxAmount': {
                              $: {
                                  currencyID: currencyName
                              },
                              _: round(
                                  _.sum(item.withHoldingTaxes.map(appliedTax => Math.abs(appliedTax.appliedAmount))) *
                                      scopeRate,
                                  currencyPrecision
                              )
                          },
                          'cac:TaxSubtotal': groupedTaxes(item.withHoldingTaxes).map((appliedTax, index) => {
                              return {
                                  ...(appliedTax.computation === 'percent'
                                      ? {
                                            'cbc:TaxableAmount': {
                                                $: {
                                                    currencyID: currencyName
                                                },
                                                _:
                                                    appliedTax.unAppliedAmount > 0
                                                        ? Math.abs(
                                                              round(
                                                                  appliedTax.unAppliedAmount * scopeRate,
                                                                  currencyPrecision
                                                              )
                                                          )
                                                        : 0
                                            }
                                        }
                                      : {}),
                                  'cbc:TaxAmount': {
                                      $: {
                                          currencyID: currencyName
                                      },
                                      _: round(Math.abs(appliedTax.appliedAmount) * scopeRate, currencyPrecision)
                                  },
                                  ...(appliedTax.computation === 'percent'
                                      ? {
                                            'cbc:Percent': appliedTax.amount
                                        }
                                      : {}),
                                  // 'cbc:CalculationSequenceNumeric': index + 1,
                                  'cac:TaxCategory': {
                                      'cac:TaxScheme': {
                                          'cbc:Name': taxes[appliedTax._id].label
                                              ? taxes[appliedTax._id].label
                                              : appliedTax.label,
                                          'cbc:TaxTypeCode': taxCodes[appliedTax._id]
                                      }
                                  }
                              };
                          })
                      }
                  }
                : {}),
            ...(!!app.setting('eops.outputDetailedItems')
                ? {
                      'cac:Item': {
                          'cbc:Description': item.description,
                          'cbc:Name': product.definition,
                          ...(!!item.hasSurplusGood && !!item.surplusGoodDescription
                              ? {
                                    'cbc:Keyword': item.surplusGoodDescription || ''
                                }
                              : {}),
                          ...(!!product.brand
                              ? {
                                    'cbc:BrandName': product.brand.name
                                }
                              : {}),
                          ...(!!item.customerCatalogNo
                              ? {
                                    'cac:BuyersItemIdentification': {
                                        'cbc:ID': item.customerCatalogNo
                                    }
                                }
                              : {}),
                          ...(!!supplierCatalogNo
                              ? {
                                    'cac:BuyersItemIdentification': {
                                        'cbc:ID': supplierCatalogNo
                                    }
                                }
                              : {}),
                          'cac:SellersItemIdentification': {
                              'cbc:ID': !!app.setting('eops.useProductBarcodesAsProductCodesOnEInvoices')
                                  ? item.barcode || product.barcode || product.code
                                  : product.code
                          },
                          ...(!!item.manufacturerProductCode
                              ? {
                                    'cac:ManufacturersItemIdentification': {
                                        'cbc:ID': item.manufacturerProductCode
                                    }
                                }
                              : {}),
                          ...(!!originCountry
                              ? {
                                    'cac:OriginCountry': {
                                        'cbc:IdentificationCode': originCountry.code,
                                        'cbc:Name': originCountry.name
                                    }
                                }
                              : {}),
                          ...(!!item.classificationCode && !!item.classificationVersion && !!item.classificationValue
                              ? {
                                    'cac:CommodityClassification': {
                                        'cbc:ItemClassificationCode': {
                                            $: {
                                                listName: item.classificationCode,
                                                listVersionID: item.classificationVersion
                                            },
                                            _: item.classificationValue
                                        }
                                    }
                                }
                              : {})
                      }
                  }
                : {
                      'cac:Item': {
                          'cbc:Name': item.description,
                          ...(!!item.hasSurplusGood && !!item.surplusGoodDescription
                              ? {
                                    'cbc:Keyword': item.surplusGoodDescription || ''
                                }
                              : {}),
                          'cac:SellersItemIdentification': {
                              'cbc:ID': !!app.setting('eops.useProductBarcodesAsProductCodesOnEInvoices')
                                  ? item.barcode || product.barcode || product.code
                                  : product.code
                          },
                          ...(!!originCountry
                              ? {
                                    'cac:OriginCountry': {
                                        'cbc:IdentificationCode': originCountry.code,
                                        'cbc:Name': originCountry.name
                                    }
                                }
                              : {}),
                          ...(!!item.classificationCode && !!item.classificationVersion && !!item.classificationValue
                              ? {
                                    'cac:CommodityClassification': {
                                        'cbc:ItemClassificationCode': {
                                            $: {
                                                listName: item.classificationCode,
                                                listVersionID: item.classificationVersion
                                            },
                                            _: item.classificationValue
                                        }
                                    }
                                }
                              : {})
                      }
                  }),
            'cac:Price': {
                'cbc:PriceAmount': {
                    $: {
                        currencyID: currencyName
                    },
                    _: app.round(item.unitPrice * scopeRate, 'unit-price')
                }
            }
        };

        if (scenarioCode === 'IHRACAT' || scenarioCode === 'YOLCUBERABERFATURA') {
            let shipmentObj = {};

            if (!!invoice.deliveryMethodId) {
                const deliveryMethod = await app.collection('logistics.delivery-methods').findOne({
                    _id: invoice.deliveryMethodId,
                    $disableSoftDelete: true,
                    $disableActiveCheck: true
                });

                shipmentObj = {
                    'cac:ShipmentStage': {
                        'cbc:TransportModeCode': deliveryMethod.code
                    }
                };

                // if (deliveryMethod.type === 'seaway') {
                //     shipmentObj = {
                //         'cac:ShipmentStage': {
                //             'cbc:TransportModeCode': deliveryMethod.code
                //         }
                //         // 'cac:TransportHandlingUnit': {
                //         //     'cac:TransportMeans': {
                //         //         'cac:MaritimeTransport': {
                //         //             'cbc:VesselID': invoice.imoAndMmsiNo || '',
                //         //             'cbc:VesselName': invoice.shipName || '',
                //         //             'cbc:RadioCallSignID': invoice.shipRadioCallName || '',
                //         //             'cbc:ShipsRequirements': invoice.shipRequirements || '',
                //         //             'cbc:GrossTonnageMeasure': invoice.shipGrossWeight || '',
                //         //             'cbc:NetTonnageMeasure': invoice.shipNetWeight || '',
                //         //             ...(!!invoice.shipRegistrationName
                //         //                 ? {
                //         //                       'cac:RegistryCertificateDocumentReference': {
                //         //                           'cbc:ID': invoice.shipRegistrationName || '',
                //         //                           'cbc:IssueDate': app.datetime
                //         //                               .fromJSDate(issueDate)
                //         //                               .startOf('day')
                //         //                               .toFormat('yyyy-LL-dd')
                //         //                       }
                //         //                   }
                //         //                 : {}),
                //         //             ...(!!invoice.shipPortOfRegistration
                //         //                 ? {
                //         //                       'cac:RegistryPortLocation': {
                //         //                           'cbc:ID': invoice.shipPortOfRegistration || ''
                //         //                       }
                //         //                   }
                //         //                 : {})
                //         //         }
                //         //     }
                //         // }
                //     };
                // } else if (deliveryMethod.type === 'airline') {
                //     shipmentObj = {
                //         'cac:ShipmentStage': {
                //             'cbc:TransportModeCode': deliveryMethod.code
                //         }
                //         // 'cac:TransportHandlingUnit': {
                //         //     'cac:TransportMeans': {
                //         //         'cac:AirTransport': {
                //         //             'cbc:AircraftID': invoice.aircraftNo || ''
                //         //         }
                //         //     }
                //         // }
                //     };
                // } else if (deliveryMethod.type === 'land-route') {
                //     shipmentObj = {
                //         'cac:ShipmentStage': {
                //             'cbc:TransportModeCode': deliveryMethod.code
                //         }
                //         // 'cac:TransportHandlingUnit': {
                //         //     'cac:TransportMeans': {
                //         //         'cac:RoadTransport': {
                //         //             'cbc:LicensePlateID': invoice.licensePlateNo || ''
                //         //         }
                //         //     }
                //         // }
                //     };
                // } else if (deliveryMethod.type === 'railroad') {
                //     shipmentObj = {
                //         'cac:ShipmentStage': {
                //             'cbc:TransportModeCode': deliveryMethod.code
                //         }
                //         // 'cac:TransportHandlingUnit': {
                //         //     'cac:TransportMeans': {
                //         //         'cac:RailTransport': {
                //         //             'cbc:TrainID': invoice.trainNo || '',
                //         //             'cbc:RailCarID': invoice.trainWagonNo || ''
                //         //         }
                //         //     }
                //         // }
                //     };
                // }
            }

            if (!!item.containerTypeId) {
                const containerType = await app.collection('logistics.container-types').findOne({
                    _id: item.containerTypeId,
                    $disableSoftDelete: true,
                    $disableActiveCheck: true
                });
                if (!shipmentObj['cac:TransportHandlingUnit']) {
                    shipmentObj['cac:TransportHandlingUnit'] = {};
                }

                shipmentObj['cac:TransportHandlingUnit']['cac:ActualPackage'] = {
                    'cbc:ID': 1,
                    'cbc:Quantity': item.containerQuantity || 1,
                    'cbc:PackagingTypeCode': containerType.code
                };
            }

            let deliveryAddress = item.deliveryAddress || {};
            if (_.isEmpty(deliveryAddress) || !deliveryAddress.countryId) {
                deliveryAddress = customer.address;
            }

            lineObj = {
                ...lineObj,
                'cac:Delivery': {
                    'cac:DeliveryAddress': {
                        'cbc:Room': deliveryAddress.doorNumber,
                        'cbc:StreetName': `${deliveryAddress.subDistrict} ${deliveryAddress.street}`,
                        'cbc:BuildingName': '',
                        'cbc:BuildingNumber': deliveryAddress.apartmentNumber,
                        'cbc:CitySubdivisionName': deliveryAddress.district,
                        'cbc:CityName': deliveryAddress.city,
                        'cbc:PostalZone': deliveryAddress.postalCode,
                        'cbc:Region': '',
                        // 'cbc:District': customer.address.subDistrict,
                        'cac:Country': {
                            'cbc:Name': countryNames[deliveryAddress.countryId]
                        }
                    },
                    ...(!!deliveryCondition
                        ? {
                              'cac:DeliveryTerms': {
                                  'cbc:ID': {
                                      $: {
                                          schemeID: 'INCOTERMS'
                                      },
                                      _: deliveryCondition.code
                                  }
                              }
                          }
                        : {}),
                    'cac:Shipment': {
                        'cbc:ID': '',
                        ...(!!item.hsCode
                            ? {
                                  'cac:GoodsItem': {
                                      'cbc:RequiredCustomsID': item.hsCode || ''
                                  }
                              }
                            : {}),
                        // 'cbc:DeclaredCustomsValueAmount': {
                        //     $: {
                        //         currencyID: currencyName
                        //     },
                        //     _: payableAmount
                        // },
                        ...shipmentObj
                    }
                }
            };
        }

        lineObjs.push(lineObj);

        index++;
    }
    invoiceObj.Invoice = {
        ...invoiceObj.Invoice,
        'cac:InvoiceLine': lineObjs
    };

    // Exchange rate.
    if (companyCurrencyName !== currencyName) {
        invoiceObj.Invoice = {
            ...invoiceObj.Invoice,
            'cac:PricingExchangeRate': {
                'cbc:SourceCurrencyCode': currencyName,
                'cbc:TargetCurrencyCode': companyCurrencyName,
                'cbc:CalculationRate': round(invoice.currencyRate, 4)
            }
        };
    }

    // Discount.
    invoiceObj.Invoice = {
        ...invoiceObj.Invoice,
        'cac:AllowanceCharge': {
            'cbc:ChargeIndicator': discount < 0,
            'cbc:AllowanceChargeReason': app.translate('Discount'),
            'cbc:MultiplierFactorNumeric': round(discount / 100, 2),
            'cbc:Amount': {
                $: {
                    currencyID: currencyName
                },
                _: discountAmount
            },
            'cbc:BaseAmount': {
                $: {
                    currencyID: currencyName
                },
                _: subTotal
            }
        }
    };

    // Taxes.
    invoiceObj.Invoice = {
        ...invoiceObj.Invoice,
        'cac:TaxTotal': {
            'cbc:TaxAmount': {
                $: {
                    currencyID: currencyName
                },
                _: round(
                    _.sum(invoice.appliedTaxes.map(appliedTax => Math.abs(appliedTax.appliedAmount))) * scopeRate,
                    currencyPrecision
                )
            },
            'cac:TaxSubtotal': groupedTaxes(invoice.appliedTaxes)
                .filter(
                    appliedTax =>
                        appliedTax.appliedAmount > 0 ||
                        (appliedTax.computation === 'percent' && appliedTax.amount === 0) ||
                        !!appliedTax.isStoppage
                )
                .filter(appliedTax => {
                    if (eInvoiceTypeCode === 'IHRACKAYITLI' && !!appliedTax.isStoppage) {
                        return false;
                    }

                    return true;
                })
                .map((appliedTax, index) => {
                    return {
                        ...(appliedTax.computation === 'percent'
                            ? {
                                  'cbc:TaxableAmount': {
                                      $: {currencyID: currencyName},
                                      _:
                                          appliedTax.unAppliedAmount > 0
                                              ? Math.abs(
                                                    round(appliedTax.unAppliedAmount * scopeRate, currencyPrecision)
                                                )
                                              : 0
                                  }
                              }
                            : {}),
                        'cbc:TaxAmount': {
                            $: {
                                currencyID: currencyName
                            },
                            _: round(Math.abs(appliedTax.appliedAmount) * scopeRate, currencyPrecision)
                        },
                        ...(appliedTax.computation === 'percent' ? {'cbc:Percent': appliedTax.amount} : {}),
                        'cbc:CalculationSequenceNumeric': index + 1,
                        'cac:TaxCategory': {
                            ...(!!exemptionReasonCode
                                ? {
                                      'cbc:TaxExemptionReasonCode': exemptionReasonCode,
                                      'cbc:TaxExemptionReason': exemptionReason
                                  }
                                : {}),
                            'cac:TaxScheme': {
                                'cbc:Name': taxes[appliedTax._id].label
                                    ? taxes[appliedTax._id].label
                                    : appliedTax.label,
                                'cbc:TaxTypeCode': taxCodes[appliedTax._id]
                            }
                        }
                    };
                })
        }
    };

    // Withholding taxes.
    if (withHoldingTaxes.length > 0) {
        invoiceObj.Invoice = {
            ...invoiceObj.Invoice,
            'cac:WithholdingTaxTotal': {
                'cbc:TaxAmount': {
                    $: {
                        currencyID: currencyName
                    },
                    _: round(
                        _.sum(withHoldingTaxes.map(appliedTax => Math.abs(appliedTax.appliedAmount))) * scopeRate,
                        currencyPrecision
                    )
                },
                'cac:TaxSubtotal': groupedTaxes(withHoldingTaxes).map((appliedTax, index) => {
                    return {
                        ...(appliedTax.computation === 'percent'
                            ? {
                                  'cbc:TaxableAmount': {
                                      $: {currencyID: currencyName},
                                      _:
                                          appliedTax.unAppliedAmount > 0
                                              ? Math.abs(
                                                    round(appliedTax.unAppliedAmount * scopeRate, currencyPrecision)
                                                )
                                              : 0
                                  }
                              }
                            : {}),
                        'cbc:TaxAmount': {
                            $: {
                                currencyID: currencyName
                            },
                            _: round(Math.abs(appliedTax.appliedAmount) * scopeRate, currencyPrecision)
                        },
                        ...(appliedTax.computation === 'percent' ? {'cbc:Percent': appliedTax.amount} : {}),
                        // 'cbc:CalculationSequenceNumeric': index + 1,
                        'cbc:AdditionalRate': appliedTax.additionalRate || '',
                        'cac:TaxCategory': {
                            'cac:TaxScheme': {
                                'cbc:Name': taxes[appliedTax._id].label
                                    ? taxes[appliedTax._id].label
                                    : appliedTax.label,
                                'cbc:TaxTypeCode': taxCodes[appliedTax._id]
                            }
                        }
                    };
                })
            }
        };
    }

    // Totals.
    invoiceObj.Invoice = {
        ...invoiceObj.Invoice,
        'cac:LegalMonetaryTotal': {
            'cbc:LineExtensionAmount': {
                $: {
                    currencyID: currencyName
                },
                _: subTotal
            },
            'cbc:TaxExclusiveAmount': {
                $: {
                    currencyID: currencyName
                },
                _: subTotalAfterDiscount
            },
            'cbc:TaxInclusiveAmount': {
                $: {
                    currencyID: currencyName
                },
                _: taxInclusiveAmount
            },
            'cbc:AllowanceTotalAmount': {
                $: {
                    currencyID: currencyName
                },
                _: discountAmount
            },
            ...(invoice.rounding !== 0
                ? {
                      'cbc:PayableRoundingAmount': {
                          $: {
                              currencyID: currencyName
                          },
                          _: round(invoice.rounding * scopeRate, currencyPrecision)
                      }
                  }
                : {}),
            'cbc:PayableAmount': {
                $: {
                    currencyID: currencyName
                },
                _: payableAmount
            }
        }
    };

    // require('fs').writeFileSync('test.xml', xmlBuilder.buildObject(invoiceObj));

    return {
        xml: xmlBuilder.buildObject(invoiceObj),
        invoiceUUID,
        scenarioCode
    };
}
